
#!/usr/bin/env python3
"""
All-in-one runner that preserves your existing scripts' functionality and
produces ONE merged CSV of generic names from Amazon, eBay, and Google SERP.

How it works (no code changes to your scrapers):
1) Runs your Amazon scraper: AmazonScrapGenric.py
   - This writes a combined items CSV (we set a temp path) and its generic names CSV (<out>.generic.csv).
2) Runs your eBay scraper (ebay.py) then its generic-name creator (ebay generic name cr.py)
   - These write ebay_trending_all_products.csv and ebay_trending_all_products.generic.csv
3) Runs your Google SERP extractor (google_SERP.py) then its normalizer (google serp generic cr.py)
   - These write a combined items CSV (we set a temp path) and combined_products.generic.csv
4) Merges the three *generic* CSVs into /mnt/data/all_generic_names.csv with columns:
   generic_name, source, domain, score

Usage examples:
  python all_in_one_runner.py --amazon --ebay --serp --serp-query "Trending products" --serp-num 5
  python all_in_one_runner.py --all   (shorthand for --amazon --ebay --serp)

Notes:
- This script intentionally shells out to your existing files to avoid changing behavior.
- Requires the same runtime deps as your scripts (playwright, serpapi, crawl4ai, pandas, requests, dotenv).
"""

import os
import csv
import sys
import shlex
import json
import argparse
import subprocess
from pathlib import Path

HERE = Path(__file__).resolve().parent

# ---- Default intermediate output paths (inside the same folder as this runner) ----
AMZ_OUT_COMBINED = HERE / "all_products_tmp.csv"               # Amazon writes items here
AMZ_OUT_GENERIC  = HERE / "all_products_tmp.generic.csv"       # Amazon writes generic here

EBAY_OUT_ITEMS   = HERE / "ebay_trending_all_products.csv"     # ebay.py writes items
EBAY_OUT_GENERIC = HERE / "ebay_trending_all_products.generic.csv"  # ebay generic writes here

SERP_OUT_COMBINED = HERE / "combined CSV.csv"                  # google_SERP.py writes items
SERP_OUT_GENERIC  = HERE / "combined_products.generic.csv"     # google serp generic writes here

FINAL_OUT = HERE / "all_generic_names.csv"

def _run(cmd: str, env=None) -> int:
    print(f"\n$ {cmd}")
    # On Windows, use shell=True to properly handle paths and arguments
    proc = subprocess.run(cmd, shell=True, env=env, cwd=str(HERE))
    return proc.returncode

def run_amazon(headless: bool) -> None:
    print("\n=== Running Amazon scraper ===")
    cmd = f'"{sys.executable}" "{HERE / "AmazonScrapGenric.py"}" amazon --out "{AMZ_OUT_COMBINED}"'
    if headless:
        cmd += " --headless"
    rc = _run(cmd)
    if rc != 0:
        print("⚠️ Amazon run failed (continuing).")
    else:
        print(f"✓ Amazon generic CSV should be at: {AMZ_OUT_GENERIC}")

def run_ebay() -> None:
    print("\n=== Running eBay scraper ===")
    rc = _run(f'"{sys.executable}" "{HERE / "ebay.py"}"')
    if rc != 0:
        print("⚠️ eBay scrape failed (continuing).")
        return
    # Then build generic names for eBay titles
    print("\n=== Building eBay generic names ===")
    rc = _run(f'"{sys.executable}" "{HERE / "ebay generic name cr.py"}"')
    if rc != 0:
        print("⚠️ eBay generic-name build failed (continuing).")

def run_serp(headless: bool, query_list, num: int, llm_provider: str) -> None:
    print("\n=== Running Google SERP extractor ===")
    # Build query args (allow multiple --query values)
    q_args = " ".join([f'--query {shlex.quote(q)}' for q in (query_list or [])]) or '--query "Trending products in US"'
    cmd = f'"{sys.executable}" "{HERE / "google_SERP.py"}" {q_args} --num {num} --out "{SERP_OUT_COMBINED}" --llm {shlex.quote(llm_provider)}'
    if headless:
        cmd += " --headless"
    rc = _run(cmd)
    if rc != 0:
        print("⚠️ SERP extraction failed (continuing).")
        return

    # Normalize titles to generic names via the existing script.
    print("\n=== Normalizing SERP titles to generic names ===")
    # The normalizer uses environment variables for INPUT_CSV / OUTPUT_CSV, set them:
    env = os.environ.copy()
    env["INPUT_CSV"]  = str(SERP_OUT_COMBINED)
    env["OUTPUT_CSV"] = str(SERP_OUT_GENERIC)
    rc = _run(f'"{sys.executable}" "{HERE / "google serp generic cr.py"}"', env=env)
    if rc != 0:
        print("⚠️ SERP generic-name build failed (continuing).")

def _read_csv_if_exists(path: Path):
    if not path.exists():
        print(f"… skipping missing file: {path.name}")
        return []
    rows = []
    with path.open("r", encoding="utf-8", newline="") as f:
        reader = csv.DictReader(f)
        for r in reader:
            # normalize keys to lowercase for safer handling
            rows.append({(k or "").strip().lower(): (v or "").strip() for k, v in r.items()})
    print(f"… loaded {len(rows)} rows from {path.name}")
    return rows

def merge_generic_csvs() -> None:
    print("\n=== Merging generic name CSVs ===")
    all_rows = []

    # Amazon: expects columns generic_name, domain, score (per your script)
    amz = _read_csv_if_exists(AMZ_OUT_GENERIC)
    for r in amz:
        g = r.get("generic_name","")
        if not g: 
            continue
        all_rows.append({
            "generic_name": g,
            "source": "amazon",
            "domain": r.get("domain","amazon.com") or "amazon.com",
            "score": r.get("score",""),
        })

    # eBay: expects columns generic_name[, score]; domain is ebay.com
    ebg = _read_csv_if_exists(EBAY_OUT_GENERIC)
    for r in ebg:
        g = r.get("generic_name","")
        if not g:
            continue
        all_rows.append({
            "generic_name": g,
            "source": "ebay",
            "domain": "ebay.com",
            "score": r.get("score",""),
        })

    # SERP: expects columns generic_name, source_website, score
    sg = _read_csv_if_exists(SERP_OUT_GENERIC)
    for r in sg:
        g = r.get("generic_name","")
        if not g:
            continue
        all_rows.append({
            "generic_name": g,
            "source": "serp",
            "domain": r.get("source_website",""),
            "score": r.get("score",""),
        })

    # Write merged
    FINAL_OUT.parent.mkdir(parents=True, exist_ok=True)
    with FINAL_OUT.open("w", encoding="utf-8", newline="") as f:
        writer = csv.DictWriter(f, fieldnames=["generic_name","source","domain","score"])
        writer.writeheader()
        for r in all_rows:
            writer.writerow(r)

    print(f"✅ Wrote merged file: {FINAL_OUT} ({len(all_rows)} rows)")

def main():
    p = argparse.ArgumentParser(description="Run Amazon, eBay, and SERP pipelines and merge generic names into one CSV.")
    p.add_argument("--amazon", action="store_true", help="Run the Amazon pipeline")
    p.add_argument("--ebay", action="store_true",   help="Run the eBay pipeline")
    p.add_argument("--serp", action="store_true",   help="Run the SERP pipeline")
    p.add_argument("--all",  action="store_true",   help="Shorthand for --amazon --ebay --serp")

    p.add_argument("--headless", action="store_true", help="Run browsers headless where supported")

    # SERP options
    p.add_argument("--serp-query", action="append", help="Add a Google query (can be repeated). If omitted, uses a default.")
    p.add_argument("--serp-num", type=int, default=5, help="Number of Google results per query (default: 5)")
    p.add_argument("--serp-llm", default="ollama/llama3", help="LLM provider id used by crawl4ai (default: ollama/llama3)")
    args = p.parse_args()

    if args.all:
        args.amazon = args.ebay = args.serp = True

    ran_any = False
    if args.amazon:
        run_amazon(headless=args.headless); ran_any = True
    if args.ebay:
        run_ebay(); ran_any = True
    if args.serp:
        run_serp(headless=args.headless, query_list=args.serp_query, num=args.serp_num, llm_provider=args.serp_llm); ran_any = True

    if not ran_any:
        print("Nothing selected. Use --all or one of --amazon/--ebay/--serp.")
        sys.exit(1)

    merge_generic_csvs()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nInterrupted.")
        sys.exit(130)
