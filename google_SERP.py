# #!/usr/bin/env python3
"""
SERP (Google Search) -> crawl4ai + LLM extraction -> append to a combined CSV.

This script pulls organic links from Google via SerpAPI, crawls each page with crawl4ai,
extracts product objects (name, category) with an LLM, and appends results to ONE CSV
with the columns: name, category, href, domain, source

Requirements (install):
  pip install serpapi crawl4ai pydantic python-dotenv

Env:
  export SERPAPI_KEY=your_key_here

Usage examples:
  python serp_products_to_csv.py --query "Trending products in US" --num 5 --out all_products.csv --headless
  python serp_products_to_csv.py --query "viral kitchen gadgets" "new pet accessories" --num 3 --out all_products.csv
"""

import os
import csv
import json
import argparse
from typing import List, Dict
from urllib.parse import urlparse

# ---------- Optional imports guarded ----------
def _missing(pkg: str):
    return f"Missing dependency: {pkg}. Install with: pip install {pkg}"

try:
    from pydantic import BaseModel
except Exception:
    BaseModel = None

try:
    from dotenv import load_dotenv
except Exception:
    load_dotenv = None

try:
    from crawl4ai import (
        AsyncWebCrawler,
        BrowserConfig,
        CrawlerRunConfig,
        CacheMode,
        LLMConfig,
        LLMExtractionStrategy,
    )
except Exception:
    AsyncWebCrawler = None
    BrowserConfig = None
    CrawlerRunConfig = None
    CacheMode = None
    LLMConfig = None
    LLMExtractionStrategy = None

try:
    from serpapi import GoogleSearch
except Exception:
    GoogleSearch = None


# =========================
# Shared utils
# =========================
COMBINED_FIELDS = ["name", "category", "href", "domain", "source"]

def safe_print(s: str):
    try:
        print(s, flush=True)
    except Exception:
        print(s.encode("utf-8", "ignore").decode("utf-8"), flush=True)

def append_to_combined_csv(out_csv: str, rows: List[Dict[str, str]]):
    """Append rows to the combined CSV (create with header if missing)."""
    file_exists = os.path.isfile(out_csv)
    with open(out_csv, "a", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=COMBINED_FIELDS)
        if not file_exists:
            writer.writeheader()
        sanitized = []
        for r in rows:
            sanitized.append({
                "name": (r.get("name") or "").strip(),
                "category": (r.get("category") or "").strip(),
                "href": (r.get("href") or "").strip(),
                "domain": (r.get("domain") or "").strip(),
                "source": (r.get("source") or "").strip(),
            })
        writer.writerows(sanitized)


# =========================
# Dependency guards
# =========================
def ensure_serp_available():
    if load_dotenv is None:
        raise RuntimeError(_missing("python-dotenv"))
    if GoogleSearch is None:
        raise RuntimeError(_missing("serpapi"))
    if BaseModel is None:
        raise RuntimeError(_missing("pydantic"))
    if any(x is None for x in [AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig, LLMExtractionStrategy]):
        raise RuntimeError(_missing("crawl4ai"))


# =========================
# SERP helpers
# =========================
def serp_get_links(query: str, num_results=5) -> List[str]:
    ensure_serp_available()
    if load_dotenv:
        load_dotenv()
    api_key = os.getenv("SERPAPI_KEY")
    if not api_key:
        raise ValueError("SERPAPI_KEY not found in environment variables")
    params = {"q": query, "api_key": api_key, "num": num_results, "engine": "google", "hl": "en"}
    try:
        search = GoogleSearch(params)
        results = search.get_dict()
        organic_results = results.get("organic_results", [])
        links = [item["link"] for item in organic_results[:num_results] if "link" in item]
        return links
    except Exception as e:
        safe_print(f"Error during SerpAPI search: {e}")
        return []


ALLOWED_CATEGORIES = [
    "Food", "Fitness", "Beauty", "Fashion", "Gaming", "Lifestyle",
    "Health", "Home", "Pets", "Sports", "Technology", "Travel"
]

def json_schema_model_Product():
    class _Product(BaseModel):
        name: str
        category: str
    return _Product


async def crawl_and_extract(urls: List[str], llm_provider: str, headless: bool) -> Dict[str, List[List[Dict[str, str]]]]:
    ensure_serp_available()
    llm_config = LLMConfig(provider=llm_provider, api_token=None)
    ProductModel = json_schema_model_Product()

    # >>> UPDATED INSTRUCTION BLOCK (stricter extraction, category-guarded) <<<
    instruction_text = f"""
You are extracting CONSUMER PRODUCT names from a single web page.

OUTPUT FORMAT (strict):
- Return a JSON array of objects, each with exactly:
  {{ "name": "<product name>", "category": "<one of allowed categories>" }}
- No extra keys, no prose, no commentary. If no valid products: return [].

ALLOWED CATEGORIES (must use exactly as written):
{json.dumps(ALLOWED_CATEGORIES)}

WHAT TO EXTRACT (include ONLY if ALL are true):
1) It is a concrete product (an item a consumer can buy/review/compare),
   e.g., “Ninja Air Fryer AF101”, “CeraVe Moisturizing Cream”, “Sony WH-1000XM5”.
2) You can confidently map it to ONE category from the allowed list.
3) It is relevant to the page’s topical focus.
   - If the page is about Food, extract only Food-related products.
   - If the page covers multiple topics, include only products that fit an allowed category.

WHAT TO SKIP (never include):
- Services, classes, subscriptions, apps without a physical/consumer product form.
- Companies/brands/stores (“Nike”, “Whole Foods”) without a specific product.
- Articles, guides, recipes, blog post titles, list headers, navigation/menu items.
- Generic category labels (“air fryer”, “gaming chair”) without a specific model/name.
- News, events, press releases, careers, investor pages.
- Bundles or kits without a clear product name (model/variant).

NAMING RULES:
- Use the product’s clean name as seen on the page (brand + model/variant when available).
- Remove prices, sales copy, emojis, and extra descriptors (e.g., “Best-selling”, “50% off”).
- Keep it concise; no quotes unless part of the official name.

CATEGORY RULES:
- Choose exactly one from the allowed list based on the product’s primary use:
  • Food: edible products, snacks, beverages, pantry items
  • Fitness: workout gear/equipment, exercise trackers
  • Beauty: skincare, makeup, haircare, beauty devices
  • Fashion: apparel, shoes, style accessories
  • Gaming: consoles, games, gaming peripherals
  • Lifestyle: EDC, hobby items, stationery (not clearly Home)
  • Health: health/medical/orthopedic/wellness devices
  • Home: appliances, furniture, cookware, cleaning devices, tools
  • Pets: pet food, toys, accessories
  • Sports: sporting goods, outdoor sport equipment
  • Technology: electronics and gadgets (phones, laptops, headphones, hubs)
  • Travel: luggage, travel accessories, travel-sized gear
- If uncertain or no clear match → SKIP the item (do not guess).

QUALITY FILTER:
- If the page is unrelated or has no valid products, return [].

FINAL REMINDER:
Return only the JSON array of {{name, category}} objects and nothing else.
"""

    llm_strategy = LLMExtractionStrategy(
        llm_config=llm_config,
        schema=ProductModel.model_json_schema(),
        extraction_type="schema",
        instruction=instruction_text,
        chunk_token_threshold=1000,
        overlap_rate=0.1,
        apply_chunking=True,
        input_format="markdown",
        extra_args={"temperature": 0.0, "max_tokens": 1000},
        verbose=True,
    )

    crawl_config = CrawlerRunConfig(
        extraction_strategy=llm_strategy,
        cache_mode=CacheMode.BYPASS,
        verbose=True,
    )

    browser_cfg = BrowserConfig(headless=headless, verbose=False)
    products_by_domain: Dict[str, List[List[Dict[str, str]]]] = {}

    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        for url in urls:
            domain = urlparse(url).netloc
            safe_print(f"\n🔍 Scraping: {url}")
            result = await crawler.arun(url=url, config=crawl_config)

            if result.success:
                try:
                    data = json.loads(result.extracted_content)
                    product_objs = [
                        {"name": prod["name"], "category": prod["category"]}
                        for prod in data if isinstance(prod, dict) and "name" in prod and "category" in prod
                    ]
                    products_by_domain.setdefault(domain, []).append(product_objs)
                except Exception as e:
                    safe_print(f"❌ Failed to parse extracted content: {e}")
            else:
                safe_print(f"❌ Extraction error: {result.error_message}")

    return products_by_domain


def main():
    parser = argparse.ArgumentParser(description="SERP -> crawl4ai + LLM extraction -> append to combined CSV")
    parser.add_argument("--query", required=True, nargs="+", help="One or more Google search queries")
    parser.add_argument("--num", type=int, default=5, help="Number of results to fetch per query")
    parser.add_argument("--out", default="combined CSV.csv", help="Combined CSV filename")
    parser.add_argument("--llm", default="ollama/llama3", help="LLM provider id for crawl4ai (e.g., ollama/llama3)")
    parser.add_argument("--headless", action="store_true", help="Run crawl browser headless")
    args = parser.parse_args()

    # Collect links for all queries
    all_links: List[str] = []
    for q in args.query:
        safe_print(f"\n🔍 Running query: {q}")
        try:
            links = serp_get_links(q, num_results=args.num)
        except Exception as e:
            safe_print(f"⚠️ Error fetching links for '{q}': {e}")
            links = []
        all_links.extend(links)

    # Deduplicate while preserving order
    all_links = list(dict.fromkeys(all_links))

    if not all_links:
        safe_print("⚠️ No links found for the given query/queries.")
        return

    safe_print("\n🌐 Unique links to scrape:")
    for link in all_links:
        safe_print(link)

    import asyncio
    products_grouped = asyncio.run(
        crawl_and_extract(all_links, llm_provider=args.llm, headless=args.headless)
    )

    # Flatten and append to the combined CSV
    rows: List[Dict[str, str]] = []
    for domain, groups in (products_grouped or {}).items():
        if not isinstance(groups, list):
            continue
        for group in groups:
            if not isinstance(group, list):
                continue
            for prod in group:
                if not isinstance(prod, dict):
                    continue
                name = (prod.get("name") or "").strip()
                category = (prod.get("category") or "").strip()
                if not name:
                    continue
                rows.append({
                    "name": name,
                    "category": category,
                    "href": "",      # keep parity with original: per-product link not extracted here
                    "domain": domain,
                    "source": "serp",
                })

    append_to_combined_csv(args.out, rows)
    safe_print(f"\n📦 Appended {len(rows)} SERP-extracted products to {args.out}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        safe_print("\nInterrupted by user.")
    except Exception as e:
        safe_print(f"\nError: {e}")
