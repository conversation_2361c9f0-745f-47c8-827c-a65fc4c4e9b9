
#!/usr/bin/env python3
"""
Unified Generic-Name Pipeline (single CSV output)

Goal:
- Create ONE combined product list (Amazon + eBay + SERP) → combined_all_products.csv
- Then generate generic names ONCE across that combined list → final_generic_names.csv

Design:
- We DO NOT modify your existing scrapers. We call them as-is.
- We avoid per-source generic files:
  • eBay: we do NOT call the generic script at all.
  • SERP: we do NOT call the generic script at all.
  • Amazon: its script auto-writes "<out>.generic.csv"; we delete that temp file afterward.

Final Output:
- final_generic_names.csv with columns: generic_name, domain, source, score

Usage examples:
  python unified_generic_pipeline.py --all --headless \
    --serp-query "Trending products in US" --serp-query "viral kitchen gadgets" --serp-num 5

  python unified_generic_pipeline.py --amazon --ebay        # only Amazon + eBay
  python unified_generic_pipeline.py --serp --serp-query "new pet accessories" --serp-num 3
"""

import os
import sys
import csv
import shlex
import time
import argparse
import subprocess
from typing import Dict, List
from pathlib import Path

# -------- Locations --------
HERE = Path(__file__).resolve().parent
COMBINED = HERE / "combined_all_products.csv"    # unified items list (name, category, href, domain, source)
FINAL_CSV = HERE / "final_generic_names.csv"     # single final generic names file

# Amazon temp .generic.csv we will remove if created
def amazon_generic_sidecar() -> Path:
    return Path(str(COMBINED).rsplit(".", 1)[0] + ".generic.csv")

# -------- Shared columns for items --------
COMBINED_FIELDS = ["name", "category", "href", "domain", "source"]

def _run(cmd: str, env=None) -> int:
    print(f"\\n$ {cmd}")
    return subprocess.call(shlex.split(cmd), env=env, cwd=str(HERE))

def _init_combined():
    if COMBINED.exists():
        COMBINED.unlink()
    with COMBINED.open("w", newline="", encoding="utf-8") as f:
        csv.DictWriter(f, fieldnames=COMBINED_FIELDS).writeheader()
    print(f"✓ Initialized {COMBINED.name}")

def _append_rows(rows: List[Dict[str,str]]):
    if not rows:
        return
    with COMBINED.open("a", newline="", encoding="utf-8") as f:
        w = csv.DictWriter(f, fieldnames=COMBINED_FIELDS)
        for r in rows:
            w.writerow({
                "name": (r.get("name") or "").strip(),
                "category": (r.get("category") or "").strip(),
                "href": (r.get("href") or "").strip(),
                "domain": (r.get("domain") or "").strip(),
                "source": (r.get("source") or "").strip(),
            })

# -------- Steps: Scrapers --------
def step_amazon(headless: bool):
    """
    Calls AmazonScrapGenric.py to append items into COMBINED.
    That script will also write <out>.generic.csv; we delete it after.
    """
    cmd = f'{sys.executable} "{HERE / "AmazonScrapGenric.py"}" amazon --out "{COMBINED}"'
    if headless:
        cmd += " --headless"
    rc = _run(cmd)
    if rc != 0:
        print("⚠️ Amazon step failed (continuing).")
    # remove its sidecar .generic.csv to ensure only one final CSV exists
    sidecar = amazon_generic_sidecar()
    if sidecar.exists():
        try:
            sidecar.unlink()
            print(f"🧹 Removed Amazon sidecar: {sidecar.name}")
        except Exception as e:
            print(f"⚠️ Could not remove Amazon sidecar: {e}")

def step_ebay():
    """
    Calls ebay.py to produce ebay_trending_all_products.csv and then
    appends into COMBINED as normalized item rows.
    """
    rc = _run(f'{sys.executable} "{HERE / "ebay.py"}"')
    if rc != 0:
        print("⚠️ eBay scrape failed (continuing).")
        return

    inp = HERE / "ebay_trending_all_products.csv"
    if not inp.exists():
        print("⚠️ eBay output not found; skipping append.")
        return

    import re
    def _norm(s: str) -> str:
        return re.sub(r"[^a-z0-9]", "", s.lower())

    rows: List[Dict[str,str]] = []
    with inp.open("r", encoding="utf-8", newline="") as f:
        r = csv.DictReader(f)
        cols = r.fieldnames or []
        title_col = None
        for c in cols:
            if _norm(c) in {"name","title","producttitle","productname","itemtitle","producttitle"}:
                title_col = c
                break
        if title_col is None and cols:
            for c in cols:
                n = _norm(c)
                if "title" in n or "name" in n:
                    title_col = c
                    break
        if title_col is None and cols:
            title_col = cols[0]

        for row in r:
            title = (row.get(title_col) or "").strip()
            if not title:
                continue
            rows.append({
                "name": title,
                "category": "",
                "href": "",
                "domain": "ebay.com",
                "source": "ebay",
            })

    _append_rows(rows)
    print(f"✓ Appended {len(rows)} eBay items into {COMBINED.name}")

def step_serp(headless: bool, queries: List[str], num: int, llm_provider: str):
    """
    Calls google_SERP.py to crawl and append items directly into COMBINED.
    """
    q_args = " ".join([f'--query "{q}"' for q in (queries or [])]) or '--query "Trending products in US"'
    cmd = f'{sys.executable} "{HERE / "google_SERP.py"}" {q_args} --num {num} --out "{COMBINED}" --llm "{llm_provider}"'
    if headless:
        cmd += " --headless"
    rc = _run(cmd)
    if rc != 0:
        print("⚠️ SERP step failed (continuing).")

# -------- Generic Name Builder (single pass over COMBINED) --------
def _build_generic_single_pass():
    import re, time, requests, pandas as pd
    from dotenv import load_dotenv

    load_dotenv()
    MODEL = os.getenv("LLM_MODEL", "llama3")
    OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/")
    GEN_URL = f"{OLLAMA_BASE}/api/generate"
    TAGS_URL = f"{OLLAMA_BASE}/api/tags"

    def _norm(s: str) -> str:
        return re.sub(r"[^a-z0-9]", "", (s or "").lower())

    df = pd.read_csv(COMBINED)
    df = df.loc[:, ~df.columns.str.contains(r"^Unnamed:", case=False)]

    title_col = None
    for c in df.columns:
        if _norm(c) in {"name","title","producttitle","productname","itemtitle","generic_name"}:
            title_col = c; break
    if title_col is None:
        for c in df.columns:
            n = _norm(c)
            if "title" in n or n.endswith("name") or n.startswith("name"):
                title_col = c; break
    if title_col is None:
        raise ValueError(f"Could not find a product title column in: {list(df.columns)}")

    domain_col = next((c for c in df.columns if _norm(c)=="domain"), None)
    source_col = next((c for c in df.columns if _norm(c)=="source"), None)

    SYSTEM_INSTRUCTION = """You are a function that normalizes e-commerce product titles.

Return ONLY one line per input:
- Product → Brand + Model/Core Identifier + critical spec if essential (e.g., 256GB, 55", AF101).
- Company/Brand only → brand/company canonical name.
- Else (guides/articles/blog/listicle) → empty string.

No explanations. No extra words.
"""

    def _first_line(s: str) -> str:
        for line in (s or "").splitlines():
            line = line.strip()
            if line:
                return line
        return ""

    def _strip_noise(s: str) -> str:
        if not s:
            return ""
        txt = s.strip().strip('"').strip("'")
        lower = txt.lower()
        bad_starts = ("since the title","no input","(no input","no output","i'll assume","therefore")
        if lower.startswith(bad_starts):
            return ""
        import re
        txt = re.sub(r'^\s*(title|clean brand\+model|cleaned title|output)\s*:\s*', '', txt, flags=re.I)
        return txt.strip()

    def _ollama_ok() -> bool:
        try:
            r = requests.get(TAGS_URL, timeout=4)
            r.raise_for_status()
            return True
        except Exception:
            return False

    def _ollama_one(raw_title: str, retries=1, timeout=25.0) -> str:
        if not isinstance(raw_title, str) or not raw_title.strip():
            return ""
        payload = {
            "model": MODEL,
            "prompt": f"""{SYSTEM_INSTRUCTION}

Title:
{raw_title.strip()}

Output:""",
            "stream": False,
            "options": {"temperature": 0.0, "top_p": 0.9, "repeat_penalty": 1.05, "num_predict": 64},
        }
        last = ""
        for attempt in range(retries+1):
            try:
                r = requests.post(GEN_URL, json=payload, timeout=timeout)
                r.raise_for_status()
                txt = (r.json() or {}).get("response", "")
                return _strip_noise(_first_line(txt))[:160].strip()
            except Exception:
                if attempt >= retries:
                    return last
                time.sleep(1.0 * (attempt + 1))

    titles = df[title_col]
    generic_series = titles.apply(lambda x: _ollama_one(x))

    counts = generic_series.value_counts(dropna=False)

    def _score(name: str) -> int:
        if not isinstance(name, str) or not name.strip():
            return 0
        cnt = int(counts.get(name, 0))
        if cnt <= 10: return 1
        if cnt <= 30: return 2
        return 3

    scores = generic_series.map(_score)

    out = {
        "generic_name": generic_series.astype(str).str.strip(),
        "domain": df[domain_col].astype(str).str.strip() if domain_col else "",
        "source": df[source_col].astype(str).str.strip() if source_col else "",
        "score": scores,
    }
    out_df = pd.DataFrame(out)

    out_df = out_df[out_df["generic_name"] != ""]
    out_df = out_df.drop_duplicates(subset=["generic_name","domain","source"])

    out_df.to_csv(FINAL_CSV, index=False)
    print(f"✅ Wrote {FINAL_CSV} ({len(out_df)} rows)")

# -------- CLI --------
def main():
    p = argparse.ArgumentParser(description="Build one combined product CSV, then one generic-names CSV.")
    p.add_argument("--amazon", action="store_true", help="Include Amazon")
    p.add_argument("--ebay", action="store_true", help="Include eBay")
    p.add_argument("--serp", action="store_true", help="Include Google SERP")
    p.add_argument("--all", action="store_true", help="Shortcut for --amazon --ebay --serp")
    p.add_argument("--headless", action="store_true", help="Run headless where supported")

    p.add_argument("--serp-query", action="append", help="Add a Google query (repeatable).")
    p.add_argument("--serp-num", type=int, default=5, help="Results per query (default: 5)")
    p.add_argument("--serp-llm", default="ollama/llama3", help="LLM provider id for crawl4ai (default: ollama/llama3)")

    args = p.parse_args()
    if args.all:
        args.amazon = args.ebay = args.serp = True

    if not (args.amazon or args.ebay or args.serp):
        p.error("Select at least one source: --amazon/--ebay/--serp or use --all")

    _init_combined()

    if args.amazon:
        step_amazon(headless=args.headless)
    if args.ebay:
        step_ebay()
    if args.serp:
        step_serp(headless=args.headless, queries=args.serp_query or [], num=args.serp_num, llm_provider=args.serp_llm)

    _build_generic_single_pass()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\\nInterrupted.")
        sys.exit(130)
