#!/usr/bin/env python3
"""
Amazon Best Sellers Scraper + Generic Name Builder (Standalone)

- Discovers top-level Best Sellers categories under /zgbs
- Scrapes page 1 and attempts multiple page-2 URL shapes
- Auto-scrolls to load enough items
- Extracts products with rating >= 4.0 and reviews > 100
- Appends to a CSV with columns:
  name, category, href, domain, source, rating, reviews
- Then builds generic names using a local Ollama model and outputs:
  <out>.generic.csv with columns: generic_name, score

Usage:
  python amazon_bestsellers.py amazon --out all_products.csv --headless

Dependencies:
  pip install playwright python-dotenv requests
  playwright install
Environment:
  LLM_MODEL (default: llama3)
  OLLAMA_BASE_URL (default: http://localhost:11434)
"""

import os
import re
import csv
import time
import html
import asyncio
import random
import unicodedata
from typing import List, Dict, Set, Optional, Tuple

# ---------------------- Optional imports for scraping ----------------------
try:
    from playwright.async_api import async_playwright, TimeoutError as PWTimeout, Page
except Exception:
    async_playwright = None
    PWTimeout = Exception
    Page = object  # type: ignore

# ---------------------- Optional imports for LLM post-processing ----------------------
import requests
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv

# ---------------------- Config ----------------------
AMAZON_BASE_URL = "https://www.amazon.com/Best-Sellers/zgbs"
TARGET_PER_PAGE = 10
ASIN_FMT = re.compile(r"^[A-Z0-9]{10}$")
PAGE2_SHAPES = [
    "{base}?pg=2",
    "{base}?_encoding=UTF8&pg=2",
    "{base}/ref=zg_bs_pg_2?_encoding=UTF8&pg=2",
    "{base}#pg_2",
]

COMBINED_FIELDS = ["name", "category", "href", "domain", "source", "rating", "reviews"]

# --------------- LLM config ---------------
load_dotenv()
MODEL = os.getenv("LLM_MODEL", "llama3.2:1b")
OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/")
GEN_URL = f"{OLLAMA_BASE}/api/generate"
UA = "generic-title-cleaner/1.0 (+python-requests)"

#SYSTEM_INSTRUCTION = """Remove colors and conditions from this product title. Keep brand and model only."""


SYSTEM_INSTRUCTION = """Clean this product title by keeping only the essential information:

KEEP: Brand, Model, Essential specs (storage size, screen size, key features)
REMOVE: Colors, pack quantities, conditions (New/Used), ratings, review counts, seller info

Examples:
- Apple iPhone 14 Pro Max (256GB) - Deep Purple → Apple iPhone 14 Pro Max (256GB)
- Samsung 55" QLED 4K Smart TV - Black → Samsung 55 QLED 4K Smart TV
- Sony WH-1000XM4 Wireless Noise Canceling Headphones - Black → Sony WH-1000XM4 Wireless Headphones
- Titleist Pro V1 Golf Balls (One Dozen) - White → Titleist Pro V1 Golf Balls

Return ONLY the cleaned product name, nothing else."""

#SYSTEM_INSTRUCTION = """create generic name for the products"""


# ---------------------- Utils ----------------------
def ensure_playwright_available():
    if async_playwright is None:
        raise RuntimeError(
            "Missing dependency: playwright. Install with: pip install playwright  (and run: playwright install)"
        )

def safe_print(s: str):
    try:
        print(s, flush=True)
    except Exception:
        print(s.encode("utf-8", "ignore").decode("utf-8"), flush=True)

def clean_title(s: str) -> str:
    s = html.unescape(s or "").strip().strip('"').strip()
    s = re.sub(r"\s+", " ", s)
    if " | " in s:
        s = s.split(" | ", 1)[0].strip()
    s = re.split(r":\s+", s, 1)[0]
    s = re.split(r"\s-\s", s, 1)[0]
    s = re.sub(r"\s*\([^()]{0,40}\)\s*$", "", s).strip()
    return s.strip().strip('"')

def append_to_csv(out_csv: str, rows: List[Dict[str, str]]):
    """Append rows to CSV (create with header if missing)."""
    file_exists = os.path.isfile(out_csv)
    with open(out_csv, "a", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=COMBINED_FIELDS)
        if not file_exists:
            writer.writeheader()
        sanitized = []
        for r in rows:
            sanitized.append({
                "name": (r.get("name") or "").strip(),
                "category": (r.get("category") or "").strip(),
                "href": (r.get("href") or "").strip(),
                "domain": (r.get("domain") or "").strip() or "amazon.com",
                "source": (r.get("source") or "").strip() or "amazon",
                "rating": str(r.get("rating") or "").strip(),
                "reviews": str(r.get("reviews") or "").strip(),
            })
        writer.writerows(sanitized)

# ---------------------- Core scraping helpers ----------------------
async def count_visible_products(page: Page) -> int:
    """Count distinct ASINs using data-asin and /dp/ anchors."""
    tiles = await page.locator("[data-asin]").all()
    asins: Set[str] = set()
    for t in tiles:
        asin = (await t.get_attribute("data-asin")) or ""
        asin = asin.strip().upper()
        if ASIN_FMT.match(asin):
            asins.add(asin)

    a_tags = await page.locator('a[href*="/dp/"]').all()
    for a in a_tags:
        href = (await a.get_attribute("href")) or ""
        m = re.search(r"/dp/([A-Z0-9]{10})", href.upper())
        if m:
            asins.add(m.group(1))
    return len(asins)

async def auto_scroll_until(page: Page, min_count: int) -> None:
    """Scroll until at least min_count products are visible or content stabilizes."""
    last_height = 0
    stable_rounds = 0
    while True:
        count = await count_visible_products(page)
        if count >= min_count:
            break
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.wait_for_timeout(900)
        await page.wait_for_timeout(700)
        height = await page.evaluate("document.body.scrollHeight")
        if height == last_height:
            stable_rounds += 1
        else:
            stable_rounds = 0
        last_height = height
        if stable_rounds >= 4:
            break

async def extract_products_from_dom(page: Page) -> List[Dict[str, Optional[str]]]:
    """Extract Amazon tiles with rating/reviews filters; return product dicts."""
    products: Dict[str, Dict[str, Optional[str]]] = {}

    tiles = await page.locator("[data-asin]").all()
    for t in tiles:
        asin = (await t.get_attribute("data-asin")) or ""
        asin = asin.strip().upper()
        if not ASIN_FMT.match(asin) or asin in products:
            continue

        # Title (aria-label -> img alt -> inner text)
        title = await t.get_attribute("aria-label")
        if not title:
            img = t.locator("img[alt]")
            if await img.count() > 0:
                title = await img.first.get_attribute("alt")
        if not title:
            title = (await t.inner_text()).strip()

        # Rank badge like "#1"
        rank = None
        badge = t.locator("text=/^#\\d+/")
        if await badge.count() > 0:
            txt = (await badge.first.text_content()) or ""
            m = re.match(r"#(\d+)", txt.strip())
            if m:
                try:
                    rank = int(m.group(1))
                except Exception:
                    rank = None

        # Rating (from "span.a-icon-alt", e.g., "4.6 out of 5 stars")
        rating_val = 0.0
        rating_el = t.locator("span.a-icon-alt")
        if await rating_el.count() > 0:
            rating_text = (await rating_el.first.inner_text()) or ""
            try:
                rating_val = float(rating_text.split()[0])
            except Exception:
                rating_val = 0.0

        # Reviews (from "span.a-size-small")
        reviews_val = 0
        reviews_el = t.locator("span.a-size-small")
        if await reviews_el.count() > 0:
            reviews_text = (await reviews_el.first.inner_text()) or "0"
            try:
                reviews_val = int(reviews_text.replace(",", ""))
            except Exception:
                reviews_val = 0

        # Keep only quality items
        if rating_val >= 4.0 and reviews_val > 100:
            products[asin] = {
                "asin": asin,
                "product_url": f"https://www.amazon.com/dp/{asin}",
                "title_raw": title or "",
                "title_clean": clean_title(title or ""),
                "rank": rank,
                "rating": rating_val,
                "reviews": reviews_val,
            }

    return list(products.values())

async def go_and_capture(page: Page, url: str) -> List[Dict[str, Optional[str]]]:
    await page.goto(url, wait_until="domcontentloaded", timeout=60000)
    await page.wait_for_timeout(1200)
    await auto_scroll_until(page, TARGET_PER_PAGE)
    return await extract_products_from_dom(page)

async def discover_categories(page: Page) -> Dict[str, str]:
    """
    From the Best Sellers homepage, return a mapping: {Category Name: URL}
    Only top-level categories under /zgbs are returned.
    """
    await page.goto(AMAZON_BASE_URL, wait_until="domcontentloaded", timeout=60000)
    await page.wait_for_timeout(1500)

    mapping: Dict[str, str] = {}
    links = await page.locator("a[href*='/zgbs/']").all()
    for a in links:
        text = (await a.inner_text() or "").strip()
        href = await a.get_attribute("href") or ""
        if not text or "/zgbs/" not in href:
            continue
        # Normalize full URL
        if href.startswith("/"):
            href = "https://www.amazon.com" + href
        href = href.split("?")[0]
        mapping[text] = href

    # Heuristic to retain likely top-level categories
    pruned: Dict[str, str] = {}
    for name, url in mapping.items():
        if re.search(r"/zgbs/[^/]+/?", url):
            pruned[name] = url
    return pruned

async def scrape_category(page: Page, cat_name: str, cat_url: str) -> List[Tuple[str, Dict[str, Optional[str]]]]:
    safe_print(f"\n>>> Scraping category: {cat_name}")
    rows: List[Tuple[str, Dict[str, Optional[str]]]] = []
    seen: Set[str] = set()

    # Page 1
    p1 = await go_and_capture(page, cat_url)
    for p in p1:
        asin = (p.get("asin") or "").upper()
        if asin and asin not in seen:
            rows.append((cat_name, p))
            seen.add(asin)

    # Page 2 (try shapes)
    best_p2: List[Dict[str, Optional[str]]] = []
    for shape in PAGE2_SHAPES:
        url2 = shape.format(base=cat_url.rstrip("/"))
        try:
            p2 = await go_and_capture(page, url2)
            if len(p2) > len(best_p2):
                best_p2 = p2
            if len(best_p2) >= TARGET_PER_PAGE:
                break
        except Exception:
            continue

    for p in best_p2:
        asin = (p.get("asin") or "").upper()
        if asin and asin not in seen:
            rows.append((cat_name, p))
            seen.add(asin)

    safe_print(f"Collected {len(rows)} items for {cat_name}")
    return rows

# ---------------------- LLM post-processing helpers ----------------------
_session = requests.Session()
_session.headers.update({"User-Agent": UA, "Accept": "application/json"})

def test_ollama_connection() -> bool:
    """Test if Ollama is running and accessible."""
    try:
        response = _session.get(f"{OLLAMA_BASE}/api/tags", timeout=5.0)
        if response.status_code == 200:
            data = response.json()
            models = [m.get("name", "") for m in data.get("models", [])]
            safe_print(f"✅ Ollama is running. Available models: {models}")
            if MODEL not in models:
                safe_print(f"⚠️ Warning: Model '{MODEL}' not found. Available: {models}")
                return False
            return True
        else:
            safe_print(f"❌ Ollama responded with status {response.status_code}")
            return False
    except Exception as e:
        safe_print(f"❌ Cannot connect to Ollama at {OLLAMA_BASE}: {str(e)}")
        safe_print("Make sure Ollama is running: ollama serve")
        return False

def _clean_model_text(txt: str) -> str:
    """Clean and extract the essential product name from model response."""
    s = " ".join((txt or "").split())
    s = s.strip(' "\'·')

    # Remove unwanted characters including '#'
    s = re.sub(r'[#@$%^&*]', '', s)

    # Remove common prefixes/suffixes that the model might add
    s = re.sub(r'^(Output:|Result:|Answer:|Cleaned:|Generic:|Product:)\s*', '', s, flags=re.IGNORECASE)
    s = re.sub(r'\s*(\.|\!|\?)$', '', s)

    # Remove unwanted numbers/patterns at the beginning
    # Handles: "1. ", "2) ", "3- ", "10. ", "123) ", "1.2.3 ", "- ", ". "
    s = re.sub(r'^(\d+[\.\)\-\s]*)+', '', s)  # Multiple digits with separators
    s = re.sub(r'^[\.\-\s]+', '', s)  # Remaining dots, dashes, spaces

    # Apply degree symbol normalization
    s = re.sub(r"(\d+)\s*\*", r"\1°", s)  # 15* -> 15°
    s = re.sub(r"\s+°", "°", s)

    # Remove trailing punctuation and whitespace
    s = re.sub(r"[\s\-:]+$", "", s)

    # Clean up multiple spaces
    s = re.sub(r'\s+', ' ', s)

    # If still too long or contains unwanted patterns, try to extract the core
    if len(s) > 100 or "→" in s or "->" in s:
        # Look for patterns like "Brand Model" at the start
        parts = s.split()
        if len(parts) >= 2:
            # Take first few meaningful parts
            result_parts = []
            for part in parts[:6]:  # Limit to first 6 words
                if part.lower() not in ['the', 'a', 'an', 'with', 'for', 'in', 'on', 'at', 'by']:
                    result_parts.append(part)
                if len(result_parts) >= 4:  # Brand + Model + maybe 2 more key terms
                    break
            s = " ".join(result_parts)

    return s.strip()

def _ollama_prompt(raw_title: str) -> str:
    return f"""{SYSTEM_INSTRUCTION}

Input: {str(raw_title).strip()}
Output:"""

def _post_ollama(prompt: str, timeout: float = 30.0) -> requests.Response:
    payload = {
        "model": MODEL,
        "prompt": prompt,
        "stream": False,
        "options": {"temperature": 0.1, "top_p": 0.9, "repeat_penalty": 1.05, "num_predict": 64},
    }
    return _session.post(GEN_URL, json=payload, timeout=timeout)

def _ollama_brand_model_one(raw_title: str, retries: int = 2, timeout: float = 30.0) -> str:
    if not raw_title or not str(raw_title).strip():
        return ""
    prompt = _ollama_prompt(raw_title)
    last_err = None
    for attempt in range(retries + 1):
        try:
            r = _post_ollama(prompt, timeout=timeout)
            # Retry only on 5xx/network; fail fast on 4xx
            if 400 <= r.status_code < 500:
                safe_print(f"❌ Ollama API error {r.status_code}: {r.text}")
                r.raise_for_status()
            r.raise_for_status()
            data = r.json() or {}
            response_text = data.get("response", "")
            cleaned = _clean_model_text(response_text)
            if not cleaned.strip():
                safe_print(f"⚠️ Empty response from Ollama for title: {raw_title[:50]}...")
            return cleaned
        except Exception as e:
            last_err = e
            safe_print(f"⚠️ Ollama API attempt {attempt + 1} failed for '{raw_title[:50]}...': {str(e)}")
            if attempt >= retries:
                safe_print(f"❌ All Ollama attempts failed for '{raw_title[:50]}...'. Final error: {str(last_err)}")
                return ""
            time.sleep((1.0 * (2 ** attempt)) + random.uniform(0, 0.25))
    return ""

def _score_from_count(cnt: int) -> int:
    # <=3 -> 1, 4..8 -> 2, >8 -> 3
    if cnt <= 3:
        return 1
    if cnt <= 8:
        return 2
    return 3

def build_generic_csv_from_rows(out_csv: str, rows: List[Dict[str, str]]):
    """
    Given scraped rows (already deduped), create <out>.generic.csv with:
    generic_name, domain, score
    """
    if not rows:
        safe_print("No rows to process for generic names.")
        return

    # Test Ollama connection first
    if not test_ollama_connection():
        safe_print("❌ Skipping generic name generation due to Ollama connection issues.")
        return

    # Create DataFrame from rows for easier processing
    df = pd.DataFrame(rows)
    titles = df["name"].fillna("")
    domains = df["domain"].fillna("amazon.com")

    # Deduplicate unique titles for LLM calls
    unique_titles = pd.Index(titles.unique())
    results: Dict[str, str] = {}

    def _task(t: str) -> Tuple[str, str]:
        return t, _ollama_brand_model_one(t)

    max_workers = min(8, max(2, os.cpu_count() or 4))
    safe_print(f"Building generic names with {len(unique_titles)} unique titles (workers={max_workers})…")
    with ThreadPoolExecutor(max_workers=max_workers) as ex:
        futures = {ex.submit(_task, t): t for t in unique_titles}
        for fut in as_completed(futures):
            t, cleaned = fut.result()
            # Clean up unwanted characters and artifacts
            cleaned = cleaned.replace('#', '').strip()
            # Remove any remaining unwanted numbers/patterns at the beginning
            cleaned = re.sub(r'^(\d+[\.\)\-\s]*)+', '', cleaned).strip()
            cleaned = re.sub(r'^[\.\-\s]+', '', cleaned).strip()
            results[t] = cleaned

    generic_series = titles.map(results.get)
    counts = generic_series.value_counts(dropna=False)

    def _score(name: str) -> int:
        if not isinstance(name, str) or not name.strip():
            return 0
        return _score_from_count(int(counts.get(name, 0)))

    scores = generic_series.map(_score)

    out_generic = os.path.splitext(out_csv)[0] + ".generic.csv"
    out_df = pd.DataFrame({
        "generic_name": generic_series,
        "domain": domains,
        "score": scores
    })
    out_df.to_csv(out_generic, index=False)
    safe_print(f"✅ Wrote {out_generic} with columns ['generic_name','domain','score'].")

# ---------------------- Orchestrator ----------------------
async def amazon_best_sellers_playwright(headless: bool, out_csv: str):
    """Run the Amazon Best Sellers scraper and append to CSV, then build generic names CSV."""
    ensure_playwright_available()

    async with async_playwright() as pw:
        browser = await pw.chromium.launch(headless=headless)
        ctx = await browser.new_context(
            locale="en-US",
            user_agent=("Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                        "AppleWebKit/537.36 (KHTML, like Gecko) "
                        "Chrome/124.0.0.0 Safari/537.36"),
            viewport={"width": 1366, "height": 900},
        )
        page: Page = await ctx.new_page()

        safe_print("Discovering categories from Best Sellers homepage…")

        categories = {"Best Sellers": "https://www.amazon.com/Best-Sellers/zgbs?ref_=nav_cs_bestsellers"}
        #categories = await discover_categories(page)

        #categories = await discover_categories(page)

        if not categories:
            safe_print("❌ Could not discover categories.")
            await ctx.close()
            await browser.close()
            return

        items = sorted(categories.items(), key=lambda kv: kv[0].lower())
        safe_print(f"Found {len(items)} categories.")

        start = time.time()
        combined_rows: List[Dict[str, str]] = []

        for idx, (cat_name, cat_url) in enumerate(items, 1):
            safe_print(f"[{idx}/{len(items)}] {cat_name} -> {cat_url}")
            try:
                entries = await scrape_category(page, cat_name, cat_url)
                for cat, p in entries:
                    combined_rows.append({
                        "name": (p.get("title_clean") or p.get("title_raw") or "").strip(),
                        "category": cat,
                        "href": (p.get("product_url") or "").strip(),
                        "domain": "amazon.com",
                        "source": "amazon",
                        "rating": str(p.get("rating") or ""),
                        "reviews": str(p.get("reviews") or ""),
                    })
            except Exception as e:
                safe_print(f"⚠️ Skipping {cat_name} due to error: {e}")

        await ctx.close()
        await browser.close()
        elapsed = time.time() - start
        safe_print(f"\nFinished all categories in {elapsed:.1f}s")

    # De-duplicate by (name, href) while preserving order; filter empties
    seen_keys: Set[Tuple[str, str]] = set()
    deduped: List[Dict[str, str]] = []
    for r in combined_rows:
        key = (r.get("name", ""), r.get("href", ""))
        if key in seen_keys:
            continue
        seen_keys.add(key)
        if r.get("name"):
            deduped.append(r)

    # Append scraped results to CSV
    append_to_csv(out_csv, deduped)
    safe_print(f"\n✅ Appended {len(deduped)} Amazon products to {out_csv}")

    # Now build the generic_name + score CSV
    try:
        build_generic_csv_from_rows(out_csv, deduped)
    except Exception as e:
        safe_print(f"⚠️ Failed to build generic names CSV: {e}")

# ---------------------- CLI ----------------------
def build_arg_parser():
    import argparse
    p = argparse.ArgumentParser(description="Amazon Best Sellers → append to CSV + generic names")
    p.add_argument("cmd", choices=["amazon"], help="Command to run")
    p.add_argument("--out", default="all_products.csv", help="Output CSV filename")
    p.add_argument("--headless", action="store_true", help="Run browser headless")
    return p

async def main_async(args):
    if args.cmd == "amazon":
        await amazon_best_sellers_playwright(headless=args.headless, out_csv=args.out)
    else:
        raise ValueError("Unknown command")

def main():
    import argparse
    parser = build_arg_parser()
    args = parser.parse_args()
    try:
        asyncio.run(main_async(args))
    except KeyboardInterrupt:
        safe_print("\nInterrupted by user.")
    except Exception as e:
        safe_print(f"\nError: {e}")

if __name__ == "__main__":
    main()
