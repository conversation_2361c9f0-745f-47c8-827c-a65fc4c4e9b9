# #!/usr/bin/env python3
# # -*- coding: utf-8 -*-

# """
# Normalize product/brand names from a Google SERP-derived CSV and output:

#   generic_name, category, source_website, source_type, score

# - Reads:  INPUT_CSV  (default: "combined_products.csv")
# - Writes: OUTPUT_CSV (default: "combined_products.generic.csv")

# Scoring buckets (based on frequency of identical generic_name across the file):
#   <= 10  -> 1
#   11..30 -> 2
#   > 30   -> 3
# """

# import os
# import re
# import time
# import requests
# import pandas as pd
# from typing import Optional, List, Dict
# from urllib.parse import urlparse
# from dotenv import load_dotenv

# # --------- Config ---------
# INPUT_CSV  = os.getenv("INPUT_CSV", "combined_products.csv")
# OUTPUT_CSV = os.getenv("OUTPUT_CSV", "combined_products.generic.csv")
# VERBOSE    = True  # set False to reduce logs

# load_dotenv()
# MODEL = os.getenv("LLM_MODEL", "llama3")
# OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/")
# GEN_URL = f"{OLLAMA_BASE}/api/generate"
# TAGS_URL = f"{OLLAMA_BASE}/api/tags"

# ALLOWED_CATEGORIES: List[str] = [
#     "Food", "Fitness", "Beauty", "Fashion", "Gaming", "Lifestyle",
#     "Health", "Home", "Pets", "Sports", "Technology", "Travel"
# ]

# # Common retail → allowed category normalization
# CATEGORY_MAP = {
#     "beauty & personal care": "Beauty",
#     "beauty and personal care": "Beauty",
#     "sports & outdoors": "Sports",
#     "sports and outdoors": "Sports",
#     "home & kitchen": "Home",
#     "kitchen & dining": "Home",
#     "health & household": "Health",
#     "pet supplies": "Pets",
#     "cell phone & accessories": "Technology",
#     "computers & accessories": "Technology",
#     "video games": "Gaming",
#     "appliances": "Home",
#     "electronics": "Technology",
#     "clothing, shoes & jewelry": "Fashion",
#     "baby": "Lifestyle",
#     "arts, crafts & sewing": "Lifestyle",
#     "apparel": "Fashion",
#     "skin care": "Beauty",
#     "skincare": "Beauty",
#     "household cleaning products": "Home",
#     "musical instruments": "Lifestyle",
#     "entertainment": "Lifestyle",
# }

# # --------- Helpers ---------
# def _norm(s: str) -> str:
#     return re.sub(r"[^a-z0-9]", "", (s or "").lower())

# def _detect_title_col(cols) -> Optional[str]:
#     aliases_exact = {"name", "title", "producttitle", "productname", "itemtitle", "generic_name"}
#     for c in cols:
#         if _norm(c) in aliases_exact:
#             return c
#     for c in cols:
#         n = _norm(c)
#         if "title" in n or n.endswith("name") or n.startswith("name"):
#             return c
#     return None

# def _detect_domain_col(cols) -> Optional[str]:
#     for cand in ("domain", "source_website", "site", "website", "host"):
#         for c in cols:
#             if _norm(c) == _norm(cand):
#                 return c
#     return None

# def _detect_source_col(cols) -> Optional[str]:
#     for cand in ("source", "source_type"):
#         for c in cols:
#             if _norm(c) == _norm(cand):
#                 return c
#     return None

# def _detect_href_col(cols) -> Optional[str]:
#     for cand in ("href", "url", "link"):
#         for c in cols:
#             if _norm(c) == cand:
#                 return c
#     return None

# def _domain_from_href(href: str) -> str:
#     try:
#         netloc = urlparse(href).netloc
#         return netloc or ""
#     except Exception:
#         return ""

# def _validate_or_map_category(cat: Optional[str]) -> str:
#     cat = (cat or "").strip()
#     if not cat:
#         return ""
#     for allowed in ALLOWED_CATEGORIES:
#         if cat.lower() == allowed.lower():
#             return allowed
#     mapped = CATEGORY_MAP.get(cat.lower())
#     if mapped:
#         return mapped
#     mapped = CATEGORY_MAP.get(re.sub(r"\s*&\s*", " & ", cat.lower()))
#     if mapped:
#         return mapped
#     return ""

# # ---------- Blank/title sanitization ----------
# _BLANK_STRINGS = {"", "nan", "none", "null", "n/a", "na", "nil"}

# def _is_blank_title(s: Optional[str]) -> bool:
#     if s is None:
#         return True
#     t = str(s).strip().strip('"').strip("'").lower()
#     return (t in _BLANK_STRINGS) or (len(t) == 0)

# def _first_line(s: str) -> str:
#     for line in (s or "").splitlines():
#         line = line.strip()
#         if line:
#             return line
#     return ""

# def _strip_non_title_replies(s: str) -> str:
#     if not s:
#         return ""
#     txt = s.strip().strip('"').strip("'")
#     lower = txt.lower()
#     bad_starts = (
#         "since the title", "no input", "(no input", "no output",
#         "i'll assume", "therefore", "clean brand+model", "cleaned title",
#         "please provide", "if you provide", "in that case", "blank string"
#     )
#     if lower.startswith(bad_starts):
#         return ""
#     txt = re.sub(r'^\s*(title|clean brand\+model|cleaned title|output)\s*:\s*', '', txt, flags=re.I)
#     if any(bad in txt.lower() for bad in ("no output", "nan", "blank", "empty")):
#         return ""
#     return txt.strip()

# # ---------- LLM prompt (tighter) ----------
# SYSTEM_INSTRUCTION = """You are a function that normalizes e-commerce SERP titles.

# TASK: Given a single SERP title, output ONLY one line:

# - If it is a PRODUCT: return a concise, search-friendly generic product name:
#   Brand + Model/Core Identifier + essential spec if critical (e.g., 256GB, 55", 60°, AF101).
# - If it is a COMPANY/BRAND NAME (e.g., ends with LLC/Inc/Ltd/Corp, or a known brand): return the canonical company/brand name only.
# - If it is NOT a product or brand/company (e.g., listicles, guides, how-to, blog posts, marketing pages), output EXACTLY an empty string.

# STRICT RULES:
# - No explanations. No extra words. No placeholders. Output must be ONE LINE only.
# - Remove colors, pack counts, condition words, shipping/store info, marketing fluff.
# - Parentheses only if they contain essential identifiers like (256GB) or model codes.
# - Do not fabricate data. If unsure, output an empty string.

# PRODUCT EXAMPLES:
# Apple iPhone 14 Pro Max (256GB) - Deep Purple -> Apple iPhone 14 Pro Max (256GB)
# Ninja Air Fryer AF101 | 2024 Picks -> Ninja Air Fryer AF101
# CeraVe Moisturizing Cream (19 oz) | Best Skincare -> CeraVe Moisturizing Cream
# Sony WH-1000XM5 Noise Cancelling Headphones Review -> Sony WH-1000XM5

# COMPANY/BRAND EXAMPLES:
# Apexant Group LLC -> Apexant Group LLC
# Faraday Holdings, Inc. -> Faraday Holdings
# Petrichor Ltd -> Petrichor

# NON-PRODUCT/NON-BRAND EXAMPLES (return empty string):
# Top 21 Trending Products to Sell Online in 2025 -> 
# How to Start a Clothing Brand (Complete Guide) ->
# Best Air Fryers of 2025: Our Picks ->
# """

# def _ollama_ok() -> bool:
#     try:
#         r = requests.get(TAGS_URL, timeout=5)
#         r.raise_for_status()
#         return True
#     except Exception as e:
#         if VERBOSE:
#             print(f"⚠️ Ollama check failed at {TAGS_URL}: {e}")
#         return False

# def _ollama_brand_model(raw_title: str, retries: int = 1, timeout: float = 25.0) -> str:
#     if _is_blank_title(raw_title):
#         return ""

#     prompt = f"""{SYSTEM_INSTRUCTION}

# Title:
# {str(raw_title).strip()}

# Output:"""

#     payload: Dict = {
#         "model": MODEL,
#         "prompt": prompt,
#         "stream": False,
#         "options": {
#             "temperature": 0.0,
#             "top_p": 0.9,
#             "repeat_penalty": 1.05,
#             "num_predict": 64,
#         },
#         # "stop": ["\n\n", "\nTitle:", "\nOutput:"],
#     }

#     for attempt in range(retries + 1):
#         try:
#             r = requests.post(GEN_URL, json=payload, timeout=timeout)
#             r.raise_for_status()
#             txt = (r.json() or {}).get("response", "")
#             txt = _first_line(txt)
#             txt = _strip_non_title_replies(txt)
#             return txt[:160].strip()
#         except Exception as e:
#             if VERBOSE:
#                 print(f"⚠️ LLM call failed (attempt {attempt+1}): {e}")
#             if attempt >= retries:
#                 return ""
#             time.sleep(1.0 * (attempt + 1))
#     return ""

# def _score_from_count(cnt: int) -> int:
#     if cnt <= 10:
#         return 1
#     elif cnt <= 30:
#         return 2
#     else:
#         return 3

# # ---------- CSV reading with headerless fallback ----------
# EXPECTED5 = ["name", "category", "href", "domain", "source"]
# EXPECTED4 = ["name", "category", "domain", "source"]

# def _read_input_csv(path: str) -> pd.DataFrame:
#     """Read CSV and auto-fix when header row is missing (first data row taken as header)."""
#     df = pd.read_csv(path)
#     # If we can't find any of our known headers, assume headerless and re-read.
#     known = {"name","title","producttitle","productname","itemtitle","generic_name",
#              "category","href","url","link","domain","source","source_type","source_website"}
#     has_known = any(_norm(c) in { _norm(k) for k in known } for c in df.columns)
#     if not has_known:
#         # Guess column count from first row length
#         # Re-read headerless:
#         tmp = pd.read_csv(path, header=None)
#         ncols = tmp.shape[1]
#         if ncols >= 5:
#             tmp.columns = EXPECTED5 + [f"extra_{i}" for i in range(ncols - 5)]
#         elif ncols == 4:
#             tmp.columns = EXPECTED4
#         else:
#             # fallback generic names
#             tmp.columns = [f"col_{i}" for i in range(ncols)]
#         if VERBOSE:
#             print(f"ℹ️ Headerless CSV detected. Assigned columns: {list(tmp.columns)}")
#         return tmp
#     return df

# # --------- Run ---------
# if __name__ == "__main__":
#     if VERBOSE:
#         print(f"Reading: {INPUT_CSV}")

#     df = _read_input_csv(INPUT_CSV)

#     # Drop unnamed index columns if present
#     df = df.loc[:, ~df.columns.str.contains(r"^Unnamed:", case=False)]

#     if VERBOSE:
#         print("Columns:", list(df.columns))

#     title_col  = _detect_title_col(df.columns)
#     domain_col = _detect_domain_col(df.columns)
#     source_col = _detect_source_col(df.columns)
#     href_col   = _detect_href_col(df.columns)

#     if not title_col:
#         raise ValueError(f"❌ Could not find a product title column in: {list(df.columns)}")

#     total_rows = len(df)
#     non_blank_titles = df[title_col].apply(lambda x: not _is_blank_title(x)).sum()
#     if VERBOSE:
#         print(f"Rows: {total_rows} | Non-blank titles: {non_blank_titles} | Title column: '{title_col}'")

#     if non_blank_titles == 0:
#         print("⚠️ All titles are blank. Check CSV content.")
#         out_df = pd.DataFrame({
#             "generic_name": ["" for _ in range(total_rows)],
#             "category": ["" for _ in range(total_rows)],
#             "source_website": ["" for _ in range(total_rows)],
#             "source_type": ["serp" for _ in range(total_rows)],
#             "score": [0 for _ in range(total_rows)],
#         })
#         out_df.to_csv(OUTPUT_CSV, index=False)
#         print(f"✅ Wrote {OUTPUT_CSV} (empty titles).")
#         raise SystemExit(0)

#     if VERBOSE and not _ollama_ok():
#         print("⚠️ Ollama may not be running or reachable. Results may be blank if the model is unavailable.")

#     # Normalize names (don’t cast to str first)
#     titles = df[title_col]
#     generic_series = titles.apply(lambda x: _ollama_brand_model(x) if not _is_blank_title(x) else "")

#     # Fallback: if LLM returned empty for all non-blank inputs
#     if (generic_series.str.len() == 0).sum() == non_blank_titles:
#         if VERBOSE:
#             print("⚠️ LLM returned empty for all titles; using original titles as generic_name fallback.")
#         generic_series = titles.apply(lambda x: "" if _is_blank_title(x) else str(x).strip())

#     # Category: keep only allowed or mapped
#     cat_col = next((c for c in df.columns if _norm(c) == "category"), None)
#     if cat_col is not None:
#         category_series = df[cat_col].map(_validate_or_map_category)
#     else:
#         category_series = pd.Series([""] * len(df))

#     # Source website (domain or derived from href)
#     if domain_col:
#         source_site_series = df[domain_col].astype(str).fillna("")
#     else:
#         if href_col:
#             source_site_series = df[href_col].astype(str).map(_domain_from_href)
#         else:
#             source_site_series = pd.Series([""] * len(df))

#     # Source type
#     if source_col:
#         source_type_series = df[source_col].astype(str).fillna("")
#     else:
#         source_type_series = pd.Series(["serp"] * len(df))

#     # Frequency counts for scoring
#     counts = generic_series.value_counts(dropna=False)

#     def _score_lookup(name) -> int:
#         if not isinstance(name, str) or not name.strip():
#             return 0
#         cnt = int(counts.get(name, 0))
#         if cnt <= 10:
#             return 1
#         elif cnt <= 30:
#             return 2
#         else:
#             return 3

#     scores = generic_series.map(_score_lookup)

#     # Final dataframe
#     out_df = pd.DataFrame({
#         "generic_name": generic_series,
#         "category": category_series,
#         "source_website": source_site_series,
#         "source_type": source_type_series,
#         "score": scores
#     })

#     out_df.to_csv(OUTPUT_CSV, index=False)
#     print(f"✅ Wrote {OUTPUT_CSV} with columns ['generic_name','category','source_website','source_type','score'].")



#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Normalize product/brand names from a Google SERP-derived CSV and output:

  generic_name, source_website, score

- Reads:  INPUT_CSV  (default: "combined_products.csv")
- Writes: OUTPUT_CSV (default: "combined_products.generic.min.csv")

Scoring buckets (based on frequency of identical generic_name across the file):
  <= 10  -> 1
  11..30 -> 2
  > 30   -> 3
"""

import os
import re
import time
import requests
import pandas as pd
from typing import Optional, List, Dict
from urllib.parse import urlparse
from dotenv import load_dotenv

# --------- Config ---------
INPUT_CSV  = os.getenv("INPUT_CSV", "combined CSV.csv")
OUTPUT_CSV = os.getenv("OUTPUT_CSV", "combined_products.generic.csv")
VERBOSE    = True  # set False to reduce logs

# Optional: drop rows where score == 0 (set DROP_SCORE_ZERO=1 in env to enable)
DROP_SCORE_ZERO = os.getenv("DROP_SCORE_ZERO", "0").lower() in ("1", "true", "yes")

load_dotenv()
MODEL = os.getenv("LLM_MODEL", "llama3")
OLLAMA_BASE = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434").rstrip("/")
GEN_URL = f"{OLLAMA_BASE}/api/generate"
TAGS_URL = f"{OLLAMA_BASE}/api/tags"

# --------- Helpers ---------
def _norm(s: str) -> str:
    return re.sub(r"[^a-z0-9]", "", (s or "").lower())

def _detect_title_col(cols) -> Optional[str]:
    aliases_exact = {"name", "title", "producttitle", "productname", "itemtitle", "generic_name"}
    for c in cols:
        if _norm(c) in aliases_exact:
            return c
    for c in cols:
        n = _norm(c)
        if "title" in n or n.endswith("name") or n.startswith("name"):
            return c
    return None

def _detect_domain_col(cols) -> Optional[str]:
    for cand in ("domain", "source_website", "site", "website", "host"):
        for c in cols:
            if _norm(c) == _norm(cand):
                return c
    return None

def _detect_source_col(cols) -> Optional[str]:
    for cand in ("source", "source_type"):
        for c in cols:
            if _norm(c) == _norm(cand):
                return c
    return None

def _detect_href_col(cols) -> Optional[str]:
    for cand in ("href", "url", "link"):
        for c in cols:
            if _norm(c) == cand:
                return c
    return None

def _domain_from_href(href: str) -> str:
    try:
        netloc = urlparse(href).netloc
        return netloc or ""
    except Exception:
        return ""

# ---------- Blank/title sanitization ----------
_BLANK_STRINGS = {"", "nan", "none", "null", "n/a", "na", "nil"}

def _is_blank_title(s: Optional[str]) -> bool:
    if s is None:
        return True
    t = str(s).strip().strip('"').strip("'").lower()
    return (t in _BLANK_STRINGS) or (len(t) == 0)

def _first_line(s: str) -> str:
    for line in (s or "").splitlines():
        line = line.strip()
        if line:
            return line
    return ""

def _strip_non_title_replies(s: str) -> str:
    if not s:
        return ""
    txt = s.strip().strip('"').strip("'")
    lower = txt.lower()
    bad_starts = (
        "since the title", "no input", "(no input", "no output",
        "i'll assume", "therefore", "clean brand+model", "cleaned title",
        "please provide", "if you provide", "in that case", "blank string"
    )
    if lower.startswith(bad_starts):
        return ""
    txt = re.sub(r'^\s*(title|clean brand\+model|cleaned title|output)\s*:\s*', '', txt, flags=re.I)
    if any(bad in txt.lower() for bad in ("no output", "nan", "blank", "empty")):
        return ""
    return txt.strip()

# ---------- LLM prompt (tighter) ----------
SYSTEM_INSTRUCTION = """You are a function that normalizes e-commerce SERP titles.

TASK: Given a single SERP title, output ONLY one line:

- If it is a PRODUCT: return a concise, search-friendly generic product name:
  Brand + Model/Core Identifier + essential spec if critical (e.g., 256GB, 55", 60°, AF101).
- If it is a COMPANY/BRAND NAME (e.g., ends with LLC/Inc/Ltd/Corp, or a known brand): return the canonical company/brand name only.
- If it is NOT a product or brand/company (e.g., listicles, guides, how-to, blog posts, marketing pages), output EXACTLY an empty string.

STRICT RULES:
- No explanations. No extra words. No placeholders. Output must be ONE LINE only.
- Remove colors, pack counts, condition words, shipping/store info, marketing fluff.
- Parentheses only if they contain essential identifiers like (256GB) or model codes.
- Do not fabricate data. If unsure, output an empty string.

PRODUCT EXAMPLES:
Apple iPhone 14 Pro Max (256GB) - Deep Purple -> Apple iPhone 14 Pro Max (256GB)
Ninja Air Fryer AF101 | 2024 Picks -> Ninja Air Fryer AF101
CeraVe Moisturizing Cream (19 oz) | Best Skincare -> CeraVe Moisturizing Cream
Sony WH-1000XM5 Noise Cancelling Headphones Review -> Sony WH-1000XM5

COMPANY/BRAND EXAMPLES:
Apexant Group LLC -> Apexant Group LLC
Faraday Holdings, Inc. -> Faraday Holdings
Petrichor Ltd -> Petrichor

NON-PRODUCT/NON-BRAND EXAMPLES (return empty string):
Top 21 Trending Products to Sell Online in 2025 -> 
How to Start a Clothing Brand (Complete Guide) ->
Best Air Fryers of 2025: Our Picks ->
"""

def _ollama_ok() -> bool:
    try:
        r = requests.get(TAGS_URL, timeout=5)
        r.raise_for_status()
        return True
    except Exception as e:
        if VERBOSE:
            print(f"⚠️ Ollama check failed at {TAGS_URL}: {e}")
        return False

def _ollama_brand_model(raw_title: str, retries: int = 1, timeout: float = 25.0) -> str:
    if _is_blank_title(raw_title):
        return ""

    prompt = f"""{SYSTEM_INSTRUCTION}

Title:
{str(raw_title).strip()}

Output:"""

    payload: Dict = {
        "model": MODEL,
        "prompt": prompt,
        "stream": False,
        "options": {
            "temperature": 0.0,
            "top_p": 0.9,
            "repeat_penalty": 1.05,
            "num_predict": 64,
        },
    }

    for attempt in range(retries + 1):
        try:
            r = requests.post(GEN_URL, json=payload, timeout=timeout)
            r.raise_for_status()
            txt = (r.json() or {}).get("response", "")
            txt = _first_line(txt)
            txt = _strip_non_title_replies(txt)
            return txt[:160].strip()
        except Exception as e:
            if VERBOSE:
                print(f"⚠️ LLM call failed (attempt {attempt+1}): {e}")
            if attempt >= retries:
                return ""
            time.sleep(1.0 * (attempt + 1))
    return ""

def _score_from_count(cnt: int) -> int:
    if cnt <= 10:
        return 1
    elif cnt <= 30:
        return 2
    else:
        return 3

# ---------- CSV reading with headerless fallback ----------
EXPECTED5 = ["name", "category", "href", "domain", "source"]
EXPECTED4 = ["name", "category", "domain", "source"]
EXPECTED3 = ["name", "domain", "source"]

def _read_input_csv(path: str) -> pd.DataFrame:
    """Read CSV and auto-fix when header row is missing (first data row taken as header)."""
    df = pd.read_csv(path)
    known = {"name","title","producttitle","productname","itemtitle","generic_name",
             "category","href","url","link","domain","source","source_type","source_website"}
    has_known = any(_norm(c) in { _norm(k) for k in known } for c in df.columns)
    if not has_known:
        tmp = pd.read_csv(path, header=None)
        ncols = tmp.shape[1]
        if ncols >= 5:
            tmp.columns = EXPECTED5 + [f"extra_{i}" for i in range(ncols - 5)]
        elif ncols == 4:
            tmp.columns = EXPECTED4
        elif ncols == 3:
            tmp.columns = EXPECTED3
        else:
            tmp.columns = [f"col_{i}" for i in range(ncols)]
        if VERBOSE:
            print(f"ℹ️ Headerless CSV detected. Assigned columns: {list(tmp.columns)}")
        return tmp
    return df

# --------- Run ---------
if __name__ == "__main__":
    if VERBOSE:
        print(f"Reading: {INPUT_CSV}")

    df = _read_input_csv(INPUT_CSV)

    # Drop unnamed index columns if present
    df = df.loc[:, ~df.columns.str.contains(r"^Unnamed:", case=False)]

    if VERBOSE:
        print("Columns:", list(df.columns))

    title_col  = _detect_title_col(df.columns)
    domain_col = _detect_domain_col(df.columns)
    source_col = _detect_source_col(df.columns)
    href_col   = _detect_href_col(df.columns)

    if not title_col:
        raise ValueError(f"❌ Could not find a product title column in: {list(df.columns)}")

    total_rows = len(df)
    non_blank_titles = df[title_col].apply(lambda x: not _is_blank_title(x)).sum()
    if VERBOSE:
        print(f"Rows: {total_rows} | Non-blank titles: {non_blank_titles} | Title column: '{title_col}'")

    if non_blank_titles == 0:
        print("⚠️ All titles are blank. Check CSV content.")
        out_df = pd.DataFrame({
            "generic_name": ["" for _ in range(total_rows)],
            "source_website": ["" for _ in range(total_rows)],
            "score": [0 for _ in range(total_rows)],
        })
        out_df.to_csv(OUTPUT_CSV, index=False)
        print(f"✅ Wrote {OUTPUT_CSV} (empty titles).")
        raise SystemExit(0)

    if VERBOSE and not _ollama_ok():
        print("⚠️ Ollama may not be running or reachable. Results may be blank if the model is unavailable.")

    # Normalize names (don’t cast to str first)
    titles = df[title_col]
    generic_series = titles.apply(lambda x: _ollama_brand_model(x) if not _is_blank_title(x) else "")

    # Fallback: if LLM returned empty for all non-blank inputs
    if (generic_series.str.len() == 0).sum() == non_blank_titles:
        if VERBOSE:
            print("⚠️ LLM returned empty for all titles; using original titles as generic_name fallback.")
        generic_series = titles.apply(lambda x: "" if _is_blank_title(x) else str(x).strip())

    # Source website (prefer 'domain', otherwise derive from 'href')
    if domain_col:
        source_site_series = df[domain_col].astype(str).fillna("")
    else:
        if href_col:
            source_site_series = df[href_col].astype(str).map(_domain_from_href)
        else:
            source_site_series = pd.Series([""] * len(df))

    # Frequency counts for scoring
    counts = generic_series.value_counts(dropna=False)

    def _score_lookup(name) -> int:
        if not isinstance(name, str) or not name.strip():
            return 0
        cnt = int(counts.get(name, 0))
        return _score_from_count(cnt)

    scores = generic_series.map(_score_lookup)

    # Final dataframe (ONLY requested columns)
    out_df = pd.DataFrame({
        "generic_name": generic_series,
        "source_website": source_site_series,
        "score": scores
    })

    # ---- FILTERS TO REMOVE BLANK LINES LIKE ",sell.amazon.com,0" ----
    # Trim whitespace
    out_df["generic_name"] = out_df["generic_name"].astype(str).str.strip()
    out_df["source_website"] = out_df["source_website"].astype(str).str.strip()

    # Drop rows with empty generic_name
    out_df = out_df[out_df["generic_name"] != ""]

    # Optional: drop score==0 rows (enable via env DROP_SCORE_ZERO=1)
    if DROP_SCORE_ZERO:
        out_df = out_df[out_df["score"] > 0]

    # Optional: drop exact duplicates
    out_df = out_df.drop_duplicates(subset=["generic_name", "source_website"])

    out_df.to_csv(OUTPUT_CSV, index=False)
    print(f"✅ Wrote {OUTPUT_CSV} with columns ['generic_name','source_website','score'].")
